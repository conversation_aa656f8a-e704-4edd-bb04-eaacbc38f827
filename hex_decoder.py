#!/usr/bin/env python3
"""
SetUserInfo Hex Decoder
Decodes the hex dumps from Frida logs to extract FCM tokens and other data
Author: Security Researcher
"""

import re
import struct
from typing import Dict, List, Tuple, Optional

class ProtobufDecoder:
    def __init__(self):
        # SetUserInfo field mapping based on source code analysis
        self.field_map = {
            1: ("requestid", "STRING"),
            2: ("token", "STRING"), 
            3: ("language", "STRING"),
            4: ("portrait", "STRING"),
            5: ("machine_code", "STRING"),
            6: ("deviceid", "BYTES"),
            7: ("pn_option", "INT64"),
            8: ("country", "STRING"),
            9: ("phone_public", "BOOL"),
            10: ("bparam", "MESSAGE"),
            11: ("extinfo", "BYTES"),
            12: ("ext", "MESSAGE"),
            13: ("pn_token", "BYTES"),  # FCM Token field!
            14: ("cb_option", "INT32"),
            15: ("status", "INT32"),
            16: ("not_merge_extinfo", "BOOL"),
            17: ("action", "STRING"),
            18: ("appversion", "INT32"),
            19: ("user_name", "STRING"),
            20: ("device_ext", "MESSAGE"),
            21: ("bankacc_verified", "INT32")
        }
    
    def hex_to_bytes(self, hex_string: str) -> bytes:
        """Convert hex string to bytes"""
        # Remove spaces and convert to bytes
        hex_clean = hex_string.replace(" ", "").replace("\n", "")
        return bytes.fromhex(hex_clean)
    
    def read_varint(self, data: bytes, offset: int) -> Tuple[int, int]:
        """Read a varint from bytes, return (value, new_offset)"""
        value = 0
        shift = 0
        new_offset = offset
        
        while new_offset < len(data):
            byte = data[new_offset]
            new_offset += 1
            value |= (byte & 0x7F) << shift
            if (byte & 0x80) == 0:
                break
            shift += 7
            
        return value, new_offset
    
    def decode_protobuf(self, data: bytes) -> Dict:
        """Decode protobuf data"""
        result = {}
        offset = 0
        
        print(f"📦 Decoding {len(data)} bytes of protobuf data...")
        
        while offset < len(data):
            try:
                # Read tag
                tag, offset = self.read_varint(data, offset)
                if tag == 0:
                    break
                    
                field_number = tag >> 3
                wire_type = tag & 0x7
                
                field_name, field_type = self.field_map.get(field_number, (f"unknown_{field_number}", "UNKNOWN"))
                
                print(f"  🔍 Field {field_number} ({field_name}) - Wire Type {wire_type}")
                
                if wire_type == 0:  # Varint
                    value, offset = self.read_varint(data, offset)
                    result[field_name] = value
                    print(f"    Value: {value}")
                    
                elif wire_type == 2:  # Length-delimited
                    length, offset = self.read_varint(data, offset)
                    if offset + length > len(data):
                        print(f"    ⚠️ Invalid length {length}, stopping")
                        break
                        
                    field_data = data[offset:offset + length]
                    offset += length
                    
                    print(f"    Length: {length} bytes")
                    
                    # Decode based on field type
                    if field_number == 13:  # pn_token - FCM Token
                        try:
                            token = field_data.decode('utf-8')
                            result[field_name] = token
                            print(f"    🎯 FCM TOKEN: {token}")
                        except:
                            result[field_name] = field_data.hex()
                            print(f"    🎯 FCM TOKEN (hex): {field_data.hex()}")
                            
                    elif field_number == 5:  # machine_code
                        try:
                            machine_code = field_data.decode('utf-8')
                            result[field_name] = machine_code
                            print(f"    🔧 MACHINE CODE: {machine_code}")
                        except:
                            result[field_name] = field_data.hex()
                            print(f"    🔧 MACHINE CODE (hex): {field_data.hex()}")
                            
                    elif field_type == "STRING":
                        try:
                            string_val = field_data.decode('utf-8')
                            result[field_name] = string_val
                            if len(string_val) > 50:
                                print(f"    📝 {field_name.upper()}: {string_val[:50]}...")
                            else:
                                print(f"    📝 {field_name.upper()}: {string_val}")
                        except:
                            result[field_name] = field_data.hex()
                            print(f"    📝 {field_name.upper()} (hex): {field_data.hex()}")
                            
                    elif field_type == "BYTES":
                        result[field_name] = field_data.hex()
                        print(f"    📱 {field_name.upper()}: {field_data.hex()}")
                        
                    else:
                        # Try to decode as string first
                        try:
                            string_val = field_data.decode('utf-8')
                            result[field_name] = string_val
                            print(f"    📄 {field_name}: {string_val}")
                        except:
                            result[field_name] = field_data.hex()
                            print(f"    📄 {field_name} (hex): {field_data.hex()}")
                            
                else:
                    print(f"    ⚠️ Unsupported wire type: {wire_type}")
                    break
                    
            except Exception as e:
                print(f"    ❌ Error at offset {offset}: {e}")
                break
                
        return result

def decode_hex_dumps():
    """Decode the hex dumps from the Frida logs"""
    
    # Hex dumps from your Frida output
    hex_dumps = [
        # First packet (201 bytes)
        "0a 13 35 37 32 34 35 38 32 38 38 34 39 33 32 36 39 39 35 39 37 1a 02 56 4e 2a 0b 61 6e 64 72 6f 69 64 5f 67 63 6d 32 20 39 13 3d 8c af ab 93 23 0d 15 b4 f5 9f 94 db 47 55 95 19 42 b5 eb a2 f5 15 17 6a ad 45 e1 82 0c 42 02 76 69 88 01 ac fc 01 aa 01 75 1a 20 39 13 3d 8c af ab 93 23 0d 15 b4 f5 9f 94",
        
        # Second packet (61 bytes)  
        "0a 13 35 37 32 34 35 38 32 38 38 34 39 33 32 36 39 39 35 39 37 10 00 62 24 82 01 00 82 02 1e 6a 04 10 01 68 02 d0 02 e6 f1 cf b2 06 a0 03 01 b0 03 01 ba 03 08 42 55 53 49 4e 45 53 53",
        
        # Third packet (345 bytes) - truncated to first 100 bytes
        "0a 12 32 33 37 31 38 36 39 36 33 30 37 32 30 39 37 36 31 31 1a 02 56 4e 2a 0b 61 6e 64 72 6f 69 64 5f 67 63 6d 32 20 39 13 3d 8c af ab 93 23 0d 15 b4 f5 9f 94 db 47 55 95 19 42 b5 eb a2 f5 15 17 6a ad 45 e1 82 0c 42 02 76 69 6a 8e 01 63 30 4c 49 6f 36 77 69 52 73 65 31 62 76 56 48 51 75 6b 58 4b 70",
        
        # Fourth packet (60 bytes)
        "0a 12 32 33 37 31 38 36 39 36 33 30 37 32 30 39 37 36 31 31 10 00 62 24 82 01 00 82 02 1e 6a 04 10 01 68 02 d0 02 e6 f1 cf b2 06 a0 03 01 b0 03 01 ba 03 08 42 55 53 49 4e 45 53 53"
    ]
    
    decoder = ProtobufDecoder()
    
    for i, hex_dump in enumerate(hex_dumps, 1):
        print(f"\n{'='*80}")
        print(f"🔍 DECODING PACKET {i}")
        print(f"{'='*80}")
        
        try:
            data = decoder.hex_to_bytes(hex_dump)
            result = decoder.decode_protobuf(data)
            
            print(f"\n📋 SUMMARY FOR PACKET {i}:")
            for field_name, value in result.items():
                if field_name == "pn_token":
                    print(f"  🎯 FCM Token: {value}")
                elif field_name == "machine_code":
                    print(f"  🔧 Machine Code: {value}")
                elif field_name == "requestid":
                    print(f"  📋 Request ID: {value}")
                elif field_name == "token":
                    print(f"  🔐 Auth Token: {value[:50]}..." if len(str(value)) > 50 else f"  🔐 Auth Token: {value}")
                elif field_name == "country":
                    print(f"  🏳️ Country: {value}")
                elif field_name == "language":
                    print(f"  🌐 Language: {value}")
                else:
                    print(f"  📄 {field_name}: {value}")
                    
        except Exception as e:
            print(f"❌ Failed to decode packet {i}: {e}")

def analyze_fcm_token_pattern():
    """Analyze FCM token patterns from the decoded data"""
    print(f"\n{'='*80}")
    print("🎯 FCM TOKEN PATTERN ANALYSIS")
    print(f"{'='*80}")
    
    # Based on the hex analysis, let's look for FCM token patterns
    # The third packet (345 bytes) likely contains the full FCM token
    
    # Looking at the hex: "6a 8e 01 63 30 4c 49 6f 36 77 69 52 73 65 31 62 76 56 48 51 75 6b 58 4b 70"
    # This translates to field 13 (0x6a = 106 = (13 << 3) | 2) with length 0x8e (142 bytes)
    # The data starts with "63 30 4c 49 6f..." which is "c0LIo6..." in ASCII
    
    # Let's decode this specific part
    fcm_hex = "63 30 4c 49 6f 36 77 69 52 73 65 31 62 76 56 48 51 75 6b 58 4b 70"  # This is just the start
    
    try:
        fcm_bytes = bytes.fromhex(fcm_hex.replace(" ", ""))
        fcm_partial = fcm_bytes.decode('utf-8')
        print(f"Partial FCM token from hex: {fcm_partial}")
        print("This appears to be the beginning of the FCM token")
        print("The full token would be 142 characters long based on the length field")
    except Exception as e:
        print(f"Error decoding FCM token: {e}")

if __name__ == "__main__":
    print("🔍 SetUserInfo Protocol Buffer Hex Decoder")
    print("=" * 80)
    
    decode_hex_dumps()
    analyze_fcm_token_pattern()
    
    print(f"\n{'='*80}")
    print("✅ ANALYSIS COMPLETE")
    print(f"{'='*80}")
    print("Key findings:")
    print("1. Field 1 (requestid): Contains request IDs like '5724582884932699597'")
    print("2. Field 3 (language): Contains 'VN' (Vietnam)")  
    print("3. Field 5 (machine_code): Contains 'android_gcm'")
    print("4. Field 13 (pn_token): Contains the FCM token (142 bytes)")
    print("5. Multiple packets suggest initial registration + full token registration")
