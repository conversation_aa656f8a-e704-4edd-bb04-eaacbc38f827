# SetUserInfo Protocol Buffer Field Mapping Analysis

## Summary
Based on analysis of the Shopee Partner app source code (smali files), the correct protocol buffer field mapping for the SetUserInfo command (0x43) has been determined.

## Corrected Field Mapping

| Field # | Hex Tag | Field Name | Data Type | Description |
|---------|---------|------------|-----------|-------------|
| 1 | 0x1 | requestid | STRING | Request identifier |
| 2 | 0x2 | token | STRING | Authentication token |
| 3 | 0x3 | country | STRING | Country code (e.g., "vi") |
| 4 | 0x4 | portrait | STRING | User portrait/avatar |
| 5 | 0x5 | machine_code | STRING | Device type (e.g., "android_gcm") |
| 6 | 0x6 | deviceid | BYTES | Device identifier |
| 7 | 0x7 | pn_option | UINT64 | Push notification options |
| 8 | 0x8 | language | STRING | Language code (e.g., "VN") |
| 9 | 0x9 | phone_public | BOOL | Phone visibility setting |
| 10 | 0xa | bparam | MESSAGE | Backend parameters |
| 13 | 0xd | pn_token | BYTES | **FCM Token field** |
| 14 | 0xe | extinfo | BYTES | Extended information |
| 15 | 0xf | status | INT32 | Status code |
| 16 | 0x10 | bankacc_verified | INT32 | Bank account verification status |
| 17 | 0x11 | appversion | INT32 | Application version |
| 18 | 0x12 | not_merge_extinfo | BOOL | Extended info merge flag |
| 19 | 0x13 | user_name | STRING | Username |
| 20 | 0x14 | cb_option | INT32 | Callback options |
| **21** | **0x15** | **ext** | **MESSAGE** | **DeviceExt message with device info** |
| 22 | 0x16 | device_ext | MESSAGE | DeviceExtinfo message |
| 100 | 0x64 | action | STRING | Action identifier |

## Key Findings

### Field 21 Correction
- **Previous assumption**: Field 21 was `bankacc_verified` (INT32)
- **Actual mapping**: Field 21 is `ext` (MESSAGE) containing DeviceExt data
- **Correct bankacc_verified**: Field 16 (0x10)

### Field 21 (DeviceExt) Contents
The DeviceExt message (field 21) contains device information including:
- Device brand (e.g., "oppo")
- Device model (e.g., "cph1911") 
- OS version (e.g., "29")
- Manufacturer information
- Device fingerprinting data
- Root detection status
- Other device characteristics

### FCM Token Location
- **Field 13 (0xd)**: `pn_token` - Contains the FCM token for push notifications
- Token format: 142-character string starting with service identifier

## Source Code Evidence
Analysis based on:
- `SetUserInfo.smali` - Main protocol buffer definition
- `SetUserInfo$Builder.smali` - Builder class with field mappings
- `DeviceExt.smali` - Device extension message structure
- `DeviceExtinfo.smali` - Additional device information structure

## Protocol Buffer Wire Format
- Uses standard protobuf wire format with varint encoding
- Field numbers encoded as (field_number << 3) | wire_type
- Length-delimited fields (wire_type = 2) for strings, bytes, and messages
- Varint fields (wire_type = 0) for integers and booleans

## Security Implications
The SetUserInfo protocol transmits comprehensive device fingerprinting data including:
1. Hardware identifiers (brand, model, device ID)
2. Software information (OS version, app version)
3. Security status (root detection, fingerprint tampering)
4. Push notification tokens for message delivery
5. User authentication and session tokens

This data enables device tracking, user identification, and security assessment by the Shopee backend systems.
