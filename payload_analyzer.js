/**
 * TCP Payload Analyzer for SetUserInfo packets
 * Extracts FCM tokens and other data from raw TCP payloads
 * Author: Security Researcher
 */

Java.perform(function () {
    console.log("[+] Starting TCP Payload Analyzer...");

    // Utility functions
    function bytesToHex(bytes) {
        let hex = "";
        for (let i = 0; i < bytes.length; i++) {
            hex += ("0" + (bytes[i] & 0xFF).toString(16)).slice(-2) + " ";
        }
        return hex.trim();
    }

    function bytesToString(bytes, encoding) {
        try {
            return Java.use("java.lang.String").$new(bytes, encoding || "UTF-8");
        } catch (e) {
            return "Error converting bytes: " + e.message;
        }
    }

    function findFCMToken(data) {
        try {
            const str = bytesToString(data);
            // FCM token pattern: prefix:APA91b[base64-like characters]
            const fcmRegex = /[A-Za-z0-9_-]{10,}:APA91b[A-Za-z0-9_-]{100,}/g;
            const matches = str.match(fcmRegex);
            return matches ? matches[0] : null;
        } catch (e) {
            return null;
        }
    }

    function findMachineCode(data) {
        try {
            const str = bytesToString(data);
            if (str.includes("android_gcm")) return "android_gcm";
            if (str.includes("android_hms")) return "android_hms";
            if (str.includes("android_gpns")) return "android_gpns";
            return null;
        } catch (e) {
            return null;
        }
    }

    function analyzeProtocolBuffer(payload) {
        console.log("    === SetUserInfo Protocol Buffer Analysis ===");

        // SetUserInfo field mapping based on source code analysis (corrected from smali)
        const fieldMap = {
            1: "requestid (STRING)",           // tag = 0x1
            2: "token (STRING)",              // tag = 0x2
            3: "country (STRING)",            // tag = 0x3
            4: "portrait (STRING)",           // tag = 0x4
            5: "machine_code (STRING)",       // tag = 0x5
            6: "deviceid (BYTES)",            // tag = 0x6
            7: "pn_option (UINT64)",          // tag = 0x7
            8: "language (STRING)",           // tag = 0x8
            9: "phone_public (BOOL)",         // tag = 0x9
            10: "bparam (MESSAGE)",           // tag = 0xa - BackendParam
            13: "pn_token (BYTES)",           // tag = 0xd - FCM Token field!
            14: "extinfo (BYTES)",            // tag = 0xe
            15: "status (INT32)",             // tag = 0xf
            16: "bankacc_verified (INT32)",   // tag = 0x10
            17: "appversion (INT32)",         // tag = 0x11
            18: "not_merge_extinfo (BOOL)",   // tag = 0x12
            19: "user_name (STRING)",         // tag = 0x13
            20: "cb_option (INT32)",          // tag = 0x14
            21: "ext (MESSAGE)",              // tag = 0x15 - DeviceExt message with device info!
            22: "device_ext (MESSAGE)",       // tag = 0x16 - DeviceExtinfo
            100: "action (STRING)"            // tag = 0x64
        };

        let offset = 0;
        let fieldCount = 0;

        while (offset < payload.length && fieldCount < 25) {
            try {
                // Read varint tag
                let tag = 0;
                let shift = 0;
                let byte;

                do {
                    if (offset >= payload.length) break;
                    byte = payload[offset++] & 0xFF;
                    tag |= (byte & 0x7F) << shift;
                    shift += 7;
                } while ((byte & 0x80) !== 0);

                if (tag === 0) break;

                const fieldNumber = tag >>> 3;
                const wireType = tag & 0x7;
                const fieldName = fieldMap[fieldNumber] || "unknown_field_" + fieldNumber;

                console.log("      Field " + fieldNumber + " (" + fieldName + "):");

                if (wireType === 0) { // Varint
                    let value = 0;
                    shift = 0;
                    do {
                        if (offset >= payload.length) break;
                        byte = payload[offset++] & 0xFF;
                        value |= (byte & 0x7F) << shift;
                        shift += 7;
                    } while ((byte & 0x80) !== 0);
                    console.log("        Value: " + value);

                } else if (wireType === 2) { // Length-delimited (strings, bytes, messages)
                    let length = 0;
                    shift = 0;
                    do {
                        if (offset >= payload.length) break;
                        byte = payload[offset++] & 0xFF;
                        length |= (byte & 0x7F) << shift;
                        shift += 7;
                    } while ((byte & 0x80) !== 0);

                    if (offset + length <= payload.length) {
                        const fieldData = Java.array('byte', Array.prototype.slice.call(payload, offset, offset + length));

                        console.log("        Length: " + length + " bytes");

                        // Special handling for known fields
                        if (fieldNumber === 13) { // pn_token - FCM Token
                            const tokenStr = bytesToString(fieldData);
                            console.log("        🎯 FCM TOKEN: " + tokenStr);

                        } else if (fieldNumber === 5) { // machine_code
                            const machineCode = bytesToString(fieldData);
                            console.log("        🔧 MACHINE CODE: " + machineCode);

                        } else if (fieldNumber === 1) { // requestid
                            const requestId = bytesToString(fieldData);
                            console.log("        📋 REQUEST ID: " + requestId);

                        } else if (fieldNumber === 2) { // token (auth token)
                            const authToken = bytesToString(fieldData);
                            console.log("        🔐 AUTH TOKEN: " + authToken.substring(0, 50) + "...");

                        } else if (fieldNumber === 3) { // language
                            const language = bytesToString(fieldData);
                            console.log("        🌐 LANGUAGE: " + language);

                        } else if (fieldNumber === 8) { // country
                            const country = bytesToString(fieldData);
                            console.log("        🏳️ COUNTRY: " + country);

                        } else if (fieldNumber === 6) { // deviceid
                            const deviceId = bytesToHex(fieldData);
                            console.log("        📱 DEVICE ID: " + deviceId);

                        } else if (fieldNumber === 21) { // ext - DeviceExt message
                            console.log("        🔧 DEVICE EXT MESSAGE (contains device brand/model/OS info):");
                            try {
                                const fieldStr = bytesToString(fieldData);
                                console.log("        Data: " + fieldStr);

                                // Try to parse the DeviceExt message
                                analyzeDeviceExtMessage(fieldData);
                            } catch (e) {
                                console.log("        Hex: " + bytesToHex(fieldData.slice(0, 50)) + "...");
                            }

                        } else {
                            // Try to decode as string first
                            try {
                                const fieldStr = bytesToString(fieldData);
                                if (fieldStr.length > 50) {
                                    console.log("        Data: " + fieldStr.substring(0, 50) + "...");
                                } else {
                                    console.log("        Data: " + fieldStr);
                                }
                            } catch (e) {
                                // If string decode fails, show hex
                                console.log("        Hex: " + bytesToHex(fieldData.slice(0, 20)) + "...");
                            }
                        }

                        offset += length;
                    } else {
                        console.log("        Invalid length, stopping analysis");
                        break;
                    }
                } else {
                    console.log("        Unsupported wire type: " + wireType);
                    break;
                }

                fieldCount++;
            } catch (e) {
                console.log("      Analysis error at offset " + offset + ": " + e.message);
                break;
            }
        }

        console.log("    === End Protocol Buffer Analysis ===");
    }

    function analyzeDeviceExtMessage(payload) {
        console.log("        === DeviceExt Message Analysis ===");

        // DeviceExt field mapping from source code analysis
        const deviceExtFieldMap = {
            1: "id (INT64)",
            2: "userid (INT32)",
            3: "deviceid (BYTES)",
            4: "device_fingerprint (BYTES)",
            5: "user_agent (STRING)",
            6: "ctime (INT32)",
            7: "mtime (INT32)",
            8: "hashed_fingerprint (BYTES)",
            9: "is_rooted (BOOL)",
            10: "is_fingerprint_tempered (BOOL)",
            11: "fingerprint_before_temper (BYTES)",
            12: "hashed_df_before_temper (BYTES)",
            13: "extinfo (BYTES)",
            14: "appid (UINT32)"
        };

        let offset = 0;
        let fieldCount = 0;

        while (offset < payload.length && fieldCount < 20) {
            try {
                // Read varint tag
                let tag = 0;
                let shift = 0;
                let byte;
                do {
                    if (offset >= payload.length) break;
                    byte = payload[offset++] & 0xFF;
                    tag |= (byte & 0x7F) << shift;
                    shift += 7;
                } while ((byte & 0x80) !== 0);

                if (tag === 0) break;

                const fieldNumber = tag >>> 3;
                const wireType = tag & 0x7;
                const fieldName = deviceExtFieldMap[fieldNumber] || "unknown_field_" + fieldNumber;

                console.log("          DeviceExt Field " + fieldNumber + " (" + fieldName + "):");

                if (wireType === 0) { // Varint
                    let value = 0;
                    shift = 0;
                    do {
                        if (offset >= payload.length) break;
                        byte = payload[offset++] & 0xFF;
                        value |= (byte & 0x7F) << shift;
                        shift += 7;
                    } while ((byte & 0x80) !== 0);

                    if (fieldNumber === 9) {
                        console.log("            🔒 IS_ROOTED: " + (value ? "YES" : "NO"));
                    } else if (fieldNumber === 10) {
                        console.log("            🔧 IS_FINGERPRINT_TEMPERED: " + (value ? "YES" : "NO"));
                    } else {
                        console.log("            Value: " + value);
                    }

                } else if (wireType === 2) { // Length-delimited
                    let length = 0;
                    shift = 0;
                    do {
                        if (offset >= payload.length) break;
                        byte = payload[offset++] & 0xFF;
                        length |= (byte & 0x7F) << shift;
                        shift += 7;
                    } while ((byte & 0x80) !== 0);

                    if (offset + length <= payload.length) {
                        const fieldData = Java.array('byte', Array.prototype.slice.call(payload, offset, offset + length));

                        if (fieldNumber === 5) { // user_agent
                            const userAgent = bytesToString(fieldData);
                            console.log("            📱 USER_AGENT: " + userAgent);
                        } else if (fieldNumber === 13) { // extinfo - contains device brand/model info
                            const extinfo = bytesToString(fieldData);
                            console.log("            📋 EXTINFO (device details): " + extinfo);
                        } else {
                            console.log("            Length: " + length + " bytes");
                            console.log("            Hex: " + bytesToHex(fieldData.slice(0, 20)) + "...");
                        }

                        offset += length;
                    } else {
                        console.log("            Invalid length, stopping DeviceExt analysis");
                        break;
                    }
                } else {
                    console.log("            Unsupported wire type: " + wireType);
                    break;
                }

                fieldCount++;
            } catch (e) {
                console.log("          DeviceExt analysis error at offset " + offset + ": " + e.message);
                break;
            }
        }

        console.log("        === End DeviceExt Analysis ===");
    }

    // Hook TCP packet creation for detailed analysis
    try {
        const TcpPacket = Java.use("com.beetalklib.network.tcp.f");

        TcpPacket.$init.overload('int', '[B').implementation = function (command, payload) {
            if (command === 0x43) {
                console.log("\n" + "=".repeat(80));
                console.log("🔍 DETAILED SetUserInfo TCP PACKET ANALYSIS");
                console.log("=".repeat(80));

                console.log("📦 Packet Info:");
                console.log("    Command: 0x" + command.toString(16) + " (" + command + ")");
                console.log("    Payload size: " + payload.length + " bytes");

                if (payload && payload.length > 0) {
                    // Extract FCM token
                    const fcmToken = findFCMToken(payload);
                    if (fcmToken) {
                        console.log("🎯 FCM Token Found: " + fcmToken);
                        console.log("    Token length: " + fcmToken.length);
                        console.log("    Token prefix: " + fcmToken.split(':')[0]);
                    }

                    // Extract machine code
                    const machineCode = findMachineCode(payload);
                    if (machineCode) {
                        console.log("🔧 Machine Code: " + machineCode);
                    }

                    // Show hex dump of first 100 bytes
                    console.log("📋 Hex Dump (first 100 bytes):");
                    const dumpSize = Math.min(payload.length, 100);
                    const hexDump = bytesToHex(Java.array('byte', Array.prototype.slice.call(payload, 0, dumpSize)));
                    console.log("    " + hexDump);

                    // Try to analyze as protocol buffer
                    try {
                        analyzeProtocolBuffer(payload);
                    } catch (e) {
                        console.log("    Protocol buffer analysis failed: " + e.message);
                    }

                    // String analysis
                    console.log("📝 String Analysis:");
                    try {
                        const payloadStr = bytesToString(payload);
                        // Replace non-printable characters with dots
                        let printableChars = "";
                        for (let i = 0; i < payloadStr.length; i++) {
                            const char = payloadStr.charAt(i);
                            const charCode = payloadStr.charCodeAt(i);
                            if (charCode >= 32 && charCode <= 126) {
                                printableChars += char;
                            } else {
                                printableChars += ".";
                            }
                        }
                        console.log("    Printable content: " + printableChars.substring(0, 200) + "...");

                        // Look for specific patterns
                        if (payloadStr.indexOf("Brand/") !== -1) {
                            const brandIndex = payloadStr.indexOf("Brand/");
                            const deviceInfo = payloadStr.substring(brandIndex, brandIndex + 100);
                            console.log("    📱 Device info: " + deviceInfo);
                        }

                        if (payloadStr.indexOf("OSVer/") !== -1) {
                            const osIndex = payloadStr.indexOf("OSVer/");
                            const osInfo = payloadStr.substring(osIndex, osIndex + 20);
                            console.log("    📱 OS Version: " + osInfo);
                        }

                        if (payloadStr.indexOf("APA91b") !== -1) {
                            const tokenIndex = payloadStr.indexOf("APA91b");
                            const tokenStart = Math.max(0, tokenIndex - 50);
                            const fcmToken = payloadStr.substring(tokenStart, tokenIndex + 100);
                            console.log("    🎯 FCM Token found in string: " + fcmToken);
                        }

                        if (payloadStr.indexOf("android_gcm") !== -1) {
                            console.log("    🔧 Contains android_gcm machine code");
                        }

                        if (payloadStr.indexOf("android_hms") !== -1) {
                            console.log("    🔧 Contains android_hms machine code");
                        }

                    } catch (e) {
                        console.log("    String analysis failed: " + e.message);
                    }
                }

                console.log("=".repeat(80) + "\n");
            }

            return this.$init(command, payload);
        };

        console.log("[+] TCP Payload Analyzer hooks installed");
    } catch (e) {
        console.log("[-] Failed to install payload analyzer: " + e.message);
    }

    // Also hook the network send to correlate with request info
    try {
        const NetworkManager = Java.use("com.shopee.app.network.e");

        NetworkManager.p.implementation = function (tcpPacket, requestId, userId, wireMessage) {
            if (tcpPacket && tcpPacket.b() === 0x43) {
                console.log("🌐 Network Context:");
                console.log("    Request ID: " + requestId);
                console.log("    User ID: " + userId);
                console.log("    Wire Message: " + (wireMessage ? wireMessage.getClass().getName() : "null"));
            }

            return this.p(tcpPacket, requestId, userId, wireMessage);
        };

        console.log("[+] Network context hooks installed");
    } catch (e) {
        console.log("[-] Failed to install network hooks: " + e.message);
    }

    console.log("[+] TCP Payload Analyzer ready!");
});
